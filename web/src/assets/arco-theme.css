/* Arco Design Theme Customization */
/* Based on MeetingPage.vue style */

:root, .arco-theme-light {
  /* Primary colors - matching MeetingPage.vue */
  --primary-1: #dbeafe;
  --primary-2: #bfdbfe;
  --primary-3: #93c5fd;
  --primary-4: #60a5fa;
  --primary-5: #3b82f6;
  --primary-6: #2563eb;
  --primary-7: #1d4ed8;
  --primary-8: #1e40af;
  --primary-9: #1e3a8a;
  
  /* Background colors */
  --color-bg-1: #f8fafc; /* light background */
  --color-bg-2: #ffffff; /* surface */
  --color-bg-3: #f1f5f9; /* dark background */
  --color-bg-4: #e2e8f0; /* border */
  --color-bg-5: #cbd5e1;
  
  /* Text colors */
  --color-text-1: #1e293b; /* text */
  --color-text-2: #334155; /* secondary text */
  --color-text-3: #64748b; /* muted text */
  --color-text-4: #94a3b8; /* placeholder text */
  
  /* Border colors */
  --color-border-1: #e2e8f0; /* light border */
  --color-border-2: #cbd5e1; /* medium border */
  --color-border-3: #94a3b8; /* dark border */
  
  /* Success colors */
  --success-1: #dcfce7;
  --success-2: #bbf7d0;
  --success-3: #86efac;
  --success-4: #4ade80;
  --success-5: #22c55e;
  --success-6: #16a34a;
  --success-7: #15803d;
  --success-8: #166534;
  --success-9: #14532d;
  
  /* Warning colors */
  --warning-1: #fef3c7;
  --warning-2: #fde68a;
  --warning-3: #fcd34d;
  --warning-4: #fbbf24;
  --warning-5: #f59e0b;
  --warning-6: #d97706;
  --warning-7: #b45309;
  --warning-8: #92400e;
  --warning-9: #78350f;
  
  /* Error colors */
  --danger-1: #fee2e2;
  --danger-2: #fecaca;
  --danger-3: #fca5a5;
  --danger-4: #f87171;
  --danger-5: #ef4444;
  --danger-6: #dc2626;
  --danger-7: #b91c1c;
  --danger-8: #991b1b;
  --danger-9: #7f1d1d;
  
  /* Neutral colors */
  --gray-1: #f8fafc;
  --gray-2: #f1f5f9;
  --gray-3: #e2e8f0;
  --gray-4: #cbd5e1;
  --gray-5: #94a3b8;
  --gray-6: #64748b;
  --gray-7: #475569;
  --gray-8: #334155;
  --gray-9: #1e293b;
  --gray-10: #0f172a;
}

/* Dark theme */
.arco-theme-dark {
  /* Primary colors */
  --primary-1: #171926;
  --primary-2: #1c2745;
  --primary-3: #213566;
  --primary-4: #284489;
  --primary-5: #2f54ae;
  --primary-6: #3b82f6;
  --primary-7: #60a5fa;
  --primary-8: #93c5fd;
  --primary-9: #dbeafe;
  
  /* Background colors */
  --color-bg-1: #0f172a; /* dark background */
  --color-bg-2: #1e293b; /* surface */
  --color-bg-3: #334155; /* dark background */
  --color-bg-4: #475569; /* border */
  --color-bg-5: #64748b;
  
  /* Text colors */
  --color-text-1: #f1f5f9; /* text */
  --color-text-2: #e2e8f0; /* secondary text */
  --color-text-3: #cbd5e1; /* muted text */
  --color-text-4: #94a3b8; /* placeholder text */
  
  /* Border colors */
  --color-border-1: #334155; /* light border */
  --color-border-2: #475569; /* medium border */
  --color-border-3: #64748b; /* dark border */
  
  /* Success colors */
  --success-1: #142d1d;
  --success-2: #184226;
  --success-3: #1d5a32;
  --success-4: #257942;
  --success-5: #2e9952;
  --success-6: #34d399;
  --success-7: #68d391;
  --success-8: #a7f3d0;
  --success-9: #d1fae5;
  
  /* Warning colors */
  --warning-1: #2d200d;
  --warning-2: #453212;
  --warning-3: #664a19;
  --warning-4: #896421;
  --warning-5: #ae7f2a;
  --warning-6: #fbbf24;
  --warning-7: #fcd34d;
  --warning-8: #fde68a;
  --warning-9: #fef3c7;
  
  /* Error colors */
  --danger-1: #2d1415;
  --danger-2: #451e20;
  --danger-3: #662c2e;
  --danger-4: #893b3d;
  --danger-5: #ae4a4d;
  --danger-6: #f87171;
  --danger-7: #fca5a5;
  --danger-8: #fecaca;
  --danger-9: #fee2e2;
  
  /* Neutral colors */
  --gray-1: #0f172a;
  --gray-2: #1e293b;
  --gray-3: #334155;
  --gray-4: #475569;
  --gray-5: #64748b;
  --gray-6: #94a3b8;
  --gray-7: #cbd5e1;
  --gray-8: #e2e8f0;
  --gray-9: #f1f5f9;
  --gray-10: #f8fafc;
}

/* Modal styles to match MeetingPage.vue */
.arco-modal {
  border-radius: 1rem !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.arco-modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--color-border-1);
}

.arco-modal-body {
  padding: 1.5rem;
}

.arco-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--color-border-1);
}

/* Button styles to match MeetingPage.vue */
.arco-btn {
  border-radius: 0.5rem !important;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.arco-btn:hover {
  transform: scale(1.05);
}

.arco-btn-primary {
  background-color: var(--primary-6) !important;
  border-color: var(--primary-6);
  color: white;
}

.arco-btn-primary:hover {
  background-color: var(--primary-7);
  border-color: var(--primary-7);
}

.arco-btn-secondary {
  background-color: var(--color-bg-2);
  border-color: var(--color-border-1);
  color: var(--color-text-1);
}

.arco-btn-secondary:hover {
  background-color: var(--color-bg-3);
  border-color: var(--color-border-2);
}

/* Form elements */
.arco-input {
  border-radius: 0.5rem;
  border: 1px solid var(--color-border-1);
  padding: 0.5rem 0.75rem;
  background-color: var(--color-bg-2);
  color: var(--color-text-1);
}

.arco-input:focus {
  border-color: var(--primary-6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Card styles */
.arco-card {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-border-1);
  background-color: var(--color-bg-2);
}

/* Custom theme classes for MeetingPage.vue style */
.bg-meeting-gradient-light {
  background: linear-gradient(135deg, var(--primary-1) 0%, var(--gray-1) 100%);
}

.bg-meeting-gradient-dark {
  background: linear-gradient(135deg, var(--primary-9) 0%, var(--gray-10) 100%);
}

/* Custom button styles */
.btn-meeting-primary {
  background-color: var(--primary-6);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-meeting-primary:hover {
  background-color: var(--primary-7);
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom input styles */
.input-meeting {
  border-radius: 0.5rem;
  border: 1px solid var(--color-border-1);
  padding: 0.75rem 1rem;
  background-color: var(--color-bg-2);
  color: var(--color-text-1);
  transition: all 0.2s ease-in-out;
}

.input-meeting:focus {
  border-color: var(--primary-6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  outline: none;
}