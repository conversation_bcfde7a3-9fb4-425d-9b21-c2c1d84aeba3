# 聊天面板收起功能实现

## 功能概述

成功实现了聊天面板的收起功能，包括：
- 右下角聊天按钮
- 未读消息计数显示
- 手机端全屏显示
- 桌面端侧边栏显示
- 平滑动画过渡

## 修改的文件

### 1. MeetingPage.vue
**主要修改：**
- 添加了 `showChatPanel` 状态控制聊天面板显示
- 添加了 `unreadMessagesCount` 计算属性统计未读消息
- 添加了 `toggleChatPanel` 方法切换面板状态
- 修改了聊天面板的布局，支持条件渲染
- 添加了右下角聊天按钮，包含未读消息计数

**关键代码：**
```vue
<!-- 聊天面板 - 可收起 -->
<ChatPanel 
  v-if="showChatPanel" 
  :class="[
    'h-full flex-shrink-0 transition-all duration-300 ease-in-out',
    'fixed md:relative z-40',
    'inset-0 md:inset-auto md:w-96',
    'bg-white dark:bg-gray-800'
  ]"
  @close="toggleChatPanel"
/>

<!-- 聊天按钮 - 右下角 -->
<button
  v-if="!showChatPanel"
  @click="toggleChatPanel"
  class="fixed bottom-6 right-6 z-50 w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-105"
>
  <ChatBubbleLeftRightIcon class="h-6 w-6" />
  <!-- 未读消息计数 -->
  <div v-if="unreadMessagesCount > 0" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center px-1">
    {{ unreadMessagesCount > 99 ? '99+' : unreadMessagesCount }}
  </div>
</button>
```

### 2. ChatPanel.vue
**主要修改：**
- 添加了聊天面板头部，包含标题和关闭按钮
- 定义了 `close` 事件，用于通知父组件关闭面板
- 导入了 `XMarkIcon` 用于关闭按钮

**关键代码：**
```vue
<!-- 聊天面板头部 -->
<div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
    {{ t('tools.webRtcMeeting.chat.title') }}
  </h3>
  <button
    @click="$emit('close')"
    class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
    :title="t('tools.webRtcMeeting.chat.close')"
  >
    <XMarkIcon class="h-5 w-5 text-gray-500 dark:text-gray-400" />
  </button>
</div>
```

### 3. meeting.ts (Store)
**主要修改：**
- 修改了 `onChatMessage` 处理，为非当前用户的消息标记为未读
- 修改了文件发送和接收的消息处理，正确设置 `read` 属性
- 确保自己发送的消息标记为已读

**关键代码：**
```typescript
webrtcService.value.onChatMessage = (message: ChatMessage) => {
  // 如果消息不是来自当前用户，标记为未读
  if (message.senderId !== clientId.value) {
    message.read = false
  } else {
    message.read = true
  }
  chatMessages.value.push(message)
}
```

### 4. WebRTCService.ts
**主要修改：**
- 修改了 `sendChatMessage` 方法，为自己发送的消息标记为已读
- 修改了 `handleDataChannelMessage` 方法，为接收到的消息标记为未读

### 5. i18n.ts
**主要修改：**
- 添加了新的翻译文本：
  - `openChat`: "打开聊天" / "Open Chat"
  - `close`: "关闭" / "Close"

## 响应式设计

### 桌面端 (md及以上)
- 聊天面板显示为右侧边栏 (`md:relative md:w-96`)
- 聊天按钮位于右下角固定位置

### 移动端 (小于md)
- 聊天面板全屏显示 (`fixed inset-0`)
- 覆盖整个屏幕，提供更好的移动体验

## CSS类说明

```css
/* 聊天面板样式 */
'h-full flex-shrink-0 transition-all duration-300 ease-in-out' /* 基础布局和动画 */
'fixed md:relative z-40' /* 移动端固定定位，桌面端相对定位 */
'inset-0 md:inset-auto md:w-96' /* 移动端全屏，桌面端固定宽度 */

/* 聊天按钮样式 */
'fixed bottom-6 right-6 z-50' /* 右下角固定位置 */
'w-14 h-14 bg-blue-600 hover:bg-blue-700' /* 尺寸和颜色 */
'rounded-full shadow-lg' /* 圆形和阴影 */
'transition-all duration-200 hover:scale-105' /* 悬停动画 */
```

## 功能特性

1. **智能未读计数**：只统计非当前用户发送的未读消息
2. **自动标记已读**：打开聊天面板时自动标记所有消息为已读
3. **平滑动画**：面板展开/收起有300ms的平滑过渡
4. **响应式设计**：自动适配移动端和桌面端
5. **无障碍支持**：按钮有适当的title属性
6. **国际化支持**：支持中英文切换

## 使用方法

1. 默认情况下聊天面板是隐藏的
2. 点击右下角的聊天按钮可以打开面板
3. 在聊天面板头部点击关闭按钮可以收起面板
4. 有新消息时，按钮右上角会显示未读消息数量
5. 在移动设备上，聊天面板会全屏显示以提供更好的体验

## 测试建议

1. 在不同屏幕尺寸下测试响应式布局
2. 测试未读消息计数功能
3. 测试聊天面板的打开/关闭动画
4. 测试多人聊天时的消息标记功能
5. 测试文件发送/接收时的消息处理
